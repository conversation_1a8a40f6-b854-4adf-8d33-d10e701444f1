server:
  port: 8082
spring:
  application:
    name: cart-service
  cloud:
    nacos:
      server-addr: *************:8848
      discovery:
        fail-fast: false
        timeout: 6000
        retry-times: 3
        # 启用服务注册
        enabled: true
      # 禁用配置中心，只使用服务发现
      config:
        enabled: false
        # 强制使用HTTP协议
        remote-first: false
    # 禁用版本兼容性检查
    compatibility-verifier:
      enabled: false
  profiles:
    active: local
  datasource:
    url: jdbc:mysql://${hm.db.host}:3306/hm-cart?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: ${hm.db.pw}
    hikari:
      maximum-pool-size: 10
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
mybatis-plus:
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
  global-config:
    db-config:
      update-strategy: not_null
      id-type: auto
logging:
  level:
    com.hmall: debug
  pattern:
    dateformat: HH:mm:ss:SSS
  file:
    path: "logs/${spring.application.name}"
knife4j:
  enable: true
  openapi:
    title: 购物车接口文档
    description: "购物车接口文档"
    email: <EMAIL>
    concat: itheima
    url: https://www.itcast.cn
    version: v1.0.0
    group:
      default:
        group-name: default
        api-rule: package
        api-rule-resources:
          - com.hmall.cart.controller