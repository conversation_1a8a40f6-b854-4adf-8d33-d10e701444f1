package com.hmall.api.client;

import com.hmall.api.dto.OrderDetailDTO;
import com.hmall.common.domain.dto.ItemDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Collection;
import java.util.List;

/**
 * ClassName：ItemClient
 * package：com.hmall.cart.client
 * Description：
 *
 * <AUTHOR>
 * @Create 2025/8/4 08:44
 * @Version 1.0
 */
@FeignClient("item-service")
public interface ItemClient {

	@GetMapping("/items")
	public  List<ItemDTO> queryItemByIds(@RequestParam("ids") Collection<Long> ids);

	@PutMapping("/items/stock/deduct")
	void deductStock(@RequestBody List<OrderDetailDTO> items);
}
