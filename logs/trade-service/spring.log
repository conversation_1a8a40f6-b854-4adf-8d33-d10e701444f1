10:05:59:376  INFO 35595 --- [main] c.a.n.client.env.SearchableProperties    : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:05:59:532  INFO 35595 --- [main] com.hmall.trade.TradeApplication         : Starting TradeApplication using Java 11.0.23 on daijunjiedeMacBook-Air.local with PID 35595 (/Users/<USER>/IdeaProjects/hmall/trade-service/target/classes started by sakurlovely in /Users/<USER>/IdeaProjects/hmall)
10:05:59:532 DEBUG 35595 --- [main] com.hmall.trade.TradeApplication         : Running with Spring Boot v2.7.12, Spring v5.3.27
10:05:59:532  INFO 35595 --- [main] com.hmall.trade.TradeApplication         : The following 1 profile is active: "dev"
10:06:00:155  INFO 35595 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=7974175d-0717-362f-8742-d1f2be9fa0e7
10:06:00:310  INFO 35595 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8084 (http)
10:06:00:314  INFO 35595 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
10:06:00:314  INFO 35595 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
10:06:00:375  INFO 35595 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
10:06:00:376  INFO 35595 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 796 ms
10:06:00:466  INFO 35595 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'item-service' URL not provided. Will try picking an instance via load-balancing.
10:06:00:626  INFO 35595 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'cart-service' URL not provided. Will try picking an instance via load-balancing.
10:06:00:850  INFO 35595 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
10:06:00:979  INFO 35595 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8084 (http) with context path ''
10:06:00:998  INFO 35595 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:06:00:998  INFO 35595 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:06:01:500  INFO 35595 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP trade-service 192.168.2.4:8084 register finished
10:06:01:504  INFO 35595 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
10:06:01:507  INFO 35595 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
10:06:01:518  INFO 35595 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
10:06:01:562  INFO 35595 --- [main] com.hmall.trade.TradeApplication         : Started TradeApplication in 2.478 seconds (JVM running for 3.225)
10:06:06:191  INFO 35595 --- [http-nio-8084-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
10:06:06:191  INFO 35595 --- [http-nio-8084-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
10:06:06:193  INFO 35595 --- [http-nio-8084-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
10:06:49:042  INFO 35595 --- [http-nio-8084-exec-10] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
10:06:49:206  INFO 35595 --- [http-nio-8084-exec-10] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
10:06:49:209 DEBUG 35595 --- [http-nio-8084-exec-10] c.h.trade.mapper.OrderMapper.selectById  : ==>  Preparing: SELECT id,total_fee,payment_type,user_id,status,create_time,pay_time,consign_time,end_time,close_time,comment_time,update_time FROM `order` WHERE id=?
10:06:49:220 DEBUG 35595 --- [http-nio-8084-exec-10] c.h.trade.mapper.OrderMapper.selectById  : ==> Parameters: 1654779387523936258(Long)
10:06:49:350 DEBUG 35595 --- [http-nio-8084-exec-10] c.h.trade.mapper.OrderMapper.selectById  : <==      Total: 1
