10:26:38:019  INFO 49372 --- [main] c.a.n.client.env.SearchableProperties    : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:26:38:089  INFO 49372 --- [main] com.hmall.user.UserApplication           : Starting UserApplication using Java 11.0.23 on daijunjiedeMacBook-Air.local with PID 49372 (/Users/<USER>/IdeaProjects/hmall/user-service/target/classes started by sakurlovely in /Users/<USER>/IdeaProjects/hmall)
10:26:38:089 DEBUG 49372 --- [main] com.hmall.user.UserApplication           : Running with Spring Boot v2.7.12, Spring v5.3.27
10:26:38:089  INFO 49372 --- [main] com.hmall.user.UserApplication           : The following 1 profile is active: "dev"
10:26:38:637  INFO 49372 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=93ddc7bb-1774-3e7e-ae4b-bf3cbc3cd8f4
10:26:38:975  INFO 49372 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8083 (http)
10:26:38:985  INFO 49372 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
10:26:38:985  INFO 49372 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
10:26:39:095  INFO 49372 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
10:26:39:095  INFO 49372 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 986 ms
10:26:39:773  INFO 49372 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
10:26:39:888  INFO 49372 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8083 (http) with context path ''
10:26:39:905  INFO 49372 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:26:39:906  INFO 49372 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:26:40:349  INFO 49372 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP user-service 192.168.2.4:8083 register finished
10:26:40:353  INFO 49372 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
10:26:40:355  INFO 49372 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
10:26:40:365  INFO 49372 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
10:26:40:408  INFO 49372 --- [main] com.hmall.user.UserApplication           : Started UserApplication in 2.607 seconds (JVM running for 2.93)
10:29:14:451  INFO 49372 --- [http-nio-8083-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
10:29:14:452  INFO 49372 --- [http-nio-8083-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
10:29:14:456  INFO 49372 --- [http-nio-8083-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
10:30:08:505  INFO 49372 --- [http-nio-8083-exec-2] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
10:30:08:756  INFO 49372 --- [http-nio-8083-exec-2] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
10:30:08:759 DEBUG 49372 --- [http-nio-8083-exec-2] c.h.user.mapper.UserMapper.selectOne     : ==>  Preparing: SELECT id,username,password,phone,create_time,update_time,status,balance FROM user WHERE (username = ?)
10:30:08:771 DEBUG 49372 --- [http-nio-8083-exec-2] c.h.user.mapper.UserMapper.selectOne     : ==> Parameters: jack(String)
10:30:08:824 DEBUG 49372 --- [http-nio-8083-exec-2] c.h.user.mapper.UserMapper.selectOne     : <==      Total: 1
