10:18:16:932  INFO 43748 --- [main] c.a.n.client.env.SearchableProperties    : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:18:17:019  INFO 43748 --- [main] com.hmall.pay.PayApplication             : Starting PayApplication using Java 11.0.23 on daijunjiedeMacBook-Air.local with PID 43748 (/Users/<USER>/IdeaProjects/hmall/pay-service/target/classes started by sakurlovely in /Users/<USER>/IdeaProjects/hmall)
10:18:17:020 DEBUG 43748 --- [main] com.hmall.pay.PayApplication             : Running with Spring Boot v2.7.12, Spring v5.3.27
10:18:17:020  INFO 43748 --- [main] com.hmall.pay.PayApplication             : The following 1 profile is active: "dev"
10:18:17:512  INFO 43748 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=d509ec6b-679a-3f96-b786-fcf5ecc758e0
10:18:17:672  INFO 43748 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8085 (http)
10:18:17:677  INFO 43748 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
10:18:17:677  INFO 43748 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
10:18:17:741  INFO 43748 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
10:18:17:742  INFO 43748 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 687 ms
10:18:17:834  INFO 43748 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'user-service' URL not provided. Will try picking an instance via load-balancing.
10:18:17:854  INFO 43748 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'trade-service' URL not provided. Will try picking an instance via load-balancing.
10:18:18:246  INFO 43748 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
10:18:18:358  INFO 43748 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8085 (http) with context path ''
10:18:18:376  INFO 43748 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:18:18:376  INFO 43748 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:18:18:828  INFO 43748 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP pay-service 192.168.2.4:8085 register finished
10:18:18:831  INFO 43748 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
10:18:18:834  INFO 43748 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
10:18:18:845  INFO 43748 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
10:18:18:885  INFO 43748 --- [main] com.hmall.pay.PayApplication             : Started PayApplication in 2.15 seconds (JVM running for 2.737)
10:19:16:098  INFO 43748 --- [http-nio-8085-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:16:099  INFO 43748 --- [http-nio-8085-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
10:19:16:101  INFO 43748 --- [http-nio-8085-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
10:19:31:000  WARN 43748 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
10:19:31:002  WARN 43748 --- [Thread-4] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
10:19:31:003  WARN 43748 --- [Thread-7] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
10:19:31:005  WARN 43748 --- [Thread-7] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
10:19:31:158  INFO 43748 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
10:19:31:174  INFO 43748 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
10:19:34:249  INFO 44644 --- [main] c.a.n.client.env.SearchableProperties    : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
10:19:34:320  INFO 44644 --- [main] com.hmall.pay.PayApplication             : Starting PayApplication using Java 11.0.23 on daijunjiedeMacBook-Air.local with PID 44644 (/Users/<USER>/IdeaProjects/hmall/pay-service/target/classes started by sakurlovely in /Users/<USER>/IdeaProjects/hmall)
10:19:34:320 DEBUG 44644 --- [main] com.hmall.pay.PayApplication             : Running with Spring Boot v2.7.12, Spring v5.3.27
10:19:34:320  INFO 44644 --- [main] com.hmall.pay.PayApplication             : The following 1 profile is active: "dev"
10:19:34:808  INFO 44644 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=d509ec6b-679a-3f96-b786-fcf5ecc758e0
10:19:35:057  INFO 44644 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8085 (http)
10:19:35:061  INFO 44644 --- [main] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
10:19:35:061  INFO 44644 --- [main] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.75]
10:19:35:119  INFO 44644 --- [main] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
10:19:35:119  INFO 44644 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 775 ms
10:19:35:206  INFO 44644 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'user-service' URL not provided. Will try picking an instance via load-balancing.
10:19:35:229  INFO 44644 --- [main] o.s.c.openfeign.FeignClientFactoryBean   : For 'trade-service' URL not provided. Will try picking an instance via load-balancing.
10:19:35:588  INFO 44644 --- [main] pertySourcedRequestMappingHandlerMapping : Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2ControllerWebMvc#getDocumentation(String, HttpServletRequest)]
10:19:35:713  INFO 44644 --- [main] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8085 (http) with context path ''
10:19:35:736  INFO 44644 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
10:19:35:736  INFO 44644 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
10:19:36:203  INFO 44644 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP pay-service 192.168.2.4:8085 register finished
10:19:36:206  INFO 44644 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Documentation plugins bootstrapped
10:19:36:208  INFO 44644 --- [main] d.s.w.p.DocumentationPluginsBootstrapper : Found 1 custom documentation plugin(s)
10:19:36:221  INFO 44644 --- [main] s.d.s.w.s.ApiListingReferenceScanner     : Scanning for api listing references
10:19:36:271  INFO 44644 --- [main] com.hmall.pay.PayApplication             : Started PayApplication in 2.216 seconds (JVM running for 2.641)
10:19:43:826  INFO 44644 --- [http-nio-8085-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:43:827  INFO 44644 --- [http-nio-8085-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
10:19:43:831  INFO 44644 --- [http-nio-8085-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
10:19:48:093  INFO 44644 --- [http-nio-8085-exec-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
10:19:48:296  INFO 44644 --- [http-nio-8085-exec-5] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
10:19:48:299 DEBUG 44644 --- [http-nio-8085-exec-5] c.h.p.mapper.PayOrderMapper.selectList   : ==>  Preparing: SELECT id,biz_order_no,pay_order_no,biz_user_id,pay_channel_code,amount,pay_type,status,expand_json,result_code,result_msg,pay_success_time,pay_over_time,qr_code_url,create_time,update_time,creater,updater,is_delete FROM pay_order
10:19:48:313 DEBUG 44644 --- [http-nio-8085-exec-5] c.h.p.mapper.PayOrderMapper.selectList   : ==> Parameters: 
10:19:48:346 DEBUG 44644 --- [http-nio-8085-exec-5] c.h.p.mapper.PayOrderMapper.selectList   : <==      Total: 2
